package com.bilibili.mall.business.account.domain.domains.account.repository;

import com.bilibili.mall.business.account.infrastructure.ad_mapper.BizAccountPrivilegeInfoMapper;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountPrivilegeInfoPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class BizAccountPrivilegeInfoRepository {

    @Resource
    private BizAccountPrivilegeInfoMapper bizAccountPrivilegeInfoMapper;


    public Integer addBizAccountPrivilege(List<Long> uidList, String operator) {
        if(CollectionUtils.isEmpty(uidList)){
            return 0;
        }

        List<BizAccountPrivilegeInfoPo> bizAccountPrivilegeInfoPos = new ArrayList<>();
        for (Long uid : uidList) {
            BizAccountPrivilegeInfoPo bizAccountPrivilegeInfoPo = new BizAccountPrivilegeInfoPo();
            bizAccountPrivilegeInfoPo.setUid(uid);
            bizAccountPrivilegeInfoPo.setOperator(operator);
            bizAccountPrivilegeInfoPo.setIsDeleted(0);
            bizAccountPrivilegeInfoPo.setCtime(new Timestamp(System.currentTimeMillis()));
            bizAccountPrivilegeInfoPo.setMtime(new Timestamp(System.currentTimeMillis()));
            bizAccountPrivilegeInfoPos.add(bizAccountPrivilegeInfoPo);
        }

        return bizAccountPrivilegeInfoMapper.insertList(bizAccountPrivilegeInfoPos);
    }

    public Integer deleteBizAccountPrivilege(List<Long> uidList) {

        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        example.createCriteria().andIn(BizAccountPrivilegeInfoPo.UID, uidList)
                .andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);

        BizAccountPrivilegeInfoPo bizAccountPrivilegeInfoPo = BizAccountPrivilegeInfoPo.builder()
                .isDeleted(1)
                .build();

        return bizAccountPrivilegeInfoMapper.updateByExampleSelective(bizAccountPrivilegeInfoPo, example);

    }

    public Integer updateBizAccountPrivilege(BizAccountPrivilegeInfoPo bizAccountPrivilegeInfoPo) {

        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        example.createCriteria().andEqualTo(BizAccountPrivilegeInfoPo.UID, bizAccountPrivilegeInfoPo.getUid())
                .andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);

        return bizAccountPrivilegeInfoMapper.updateByExample(bizAccountPrivilegeInfoPo, example);

    }

    public Integer updateBizAccountPrivilege(List<Long> uidlist, String operator) {
        if(CollectionUtils.isEmpty(uidlist)){
            return 0;
        }

        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        example.createCriteria().andIn(BizAccountPrivilegeInfoPo.UID, uidlist)
                .andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);

        BizAccountPrivilegeInfoPo bizAccountPrivilegeInfoPo = BizAccountPrivilegeInfoPo.builder()
                .operator(operator)
                .build();

        return bizAccountPrivilegeInfoMapper.updateByExampleSelective(bizAccountPrivilegeInfoPo, example);
    }


    public List<BizAccountPrivilegeInfoPo> selectBizAccountPrivilege(List<Long> uidList, Long startTime, Long endTime) {
        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        Example.Criteria criteria = example.createCriteria();
        if (CollectionUtils.isNotEmpty(uidList)) {
            criteria.andIn(BizAccountPrivilegeInfoPo.UID, uidList);
        }
        if (Objects.nonNull(startTime) && startTime > 0) {
            criteria.andGreaterThanOrEqualTo(BizAccountPrivilegeInfoPo.C_TIME, new Date(startTime));
        }
        if (Objects.nonNull(endTime) && endTime > 0) {
            criteria.andLessThanOrEqualTo(BizAccountPrivilegeInfoPo.C_TIME, new Date(endTime));
        }

        criteria.andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);

        return bizAccountPrivilegeInfoMapper.selectByExample(example);
    }


    public Integer queryAllValidPrivilegeCount() {
        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        example.createCriteria().andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);
        return bizAccountPrivilegeInfoMapper.selectCountByExample(example);
    }

    public List<BizAccountPrivilegeInfoPo> queryAllValidPrivilegeList() {
        Example example = new Example(BizAccountPrivilegeInfoPo.class);
        example.createCriteria().andEqualTo(BizAccountPrivilegeInfoPo.IS_DELETED, 0);
        return bizAccountPrivilegeInfoMapper.selectByExample(example);
    }

}
