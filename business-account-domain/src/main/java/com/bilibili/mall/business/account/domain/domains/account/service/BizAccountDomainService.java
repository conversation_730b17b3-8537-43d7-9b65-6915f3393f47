package com.bilibili.mall.business.account.domain.domains.account.service;

import com.alibaba.fastjson.JSON;
import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bilibili.mall.business.account.api.enums.AuthTypeEnum;
import com.bilibili.mall.business.account.api.enums.RoleTypeEnum;
import com.bilibili.mall.business.account.api.enums.UserOpTypeEnum;
import com.bilibili.mall.business.account.api.request.account.StaffUnbindRequest;
import com.bilibili.mall.business.account.domain.anticorruption.consumer.StaffConsumer;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AccountMemberGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdAccountInfoGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.rpcservice.AuthPlatformService;
import com.bilibili.mall.business.account.domain.constants.SequenceNames;
import com.bilibili.mall.business.account.domain.databus.dto.ToolRemoveMsg;
import com.bilibili.mall.business.account.domain.databus.pub.ToolRemoveMsgPub;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserShopInfo;
import com.bilibili.mall.business.account.domain.domains.account.entity.UserTokenInfo;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountPrivilegeInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountUserLogRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountUserRepository;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountInfo;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountPrivilegeInfoPo;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountUser;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountUserOpLog;
import com.bilibili.mall.business.account.infrastructure.exception.BizErrorCode;
import com.bilibili.mall.business.account.infrastructure.utils.FunctionUtil;
import com.bilibili.mall.common.exception.ClientViewException;
import com.bilibili.mall.common.response.BaseResponse;
import com.bilibili.mall.customer.workbench.api.dto.workbench.StaffMainDTO;
import com.bilibili.mall.customer.workbench.api.enums.CustomerBizIdEnum;
import com.bilibili.mall.customer.workbench.api.query.staff.StaffMainQuery;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.bilibili.mall.kraken.sequence.SequenceService;
import com.bilibili.warp.databus.Message;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/31 7:53 下午
 */
@Slf4j
@Service
public class BizAccountDomainService {
    @Resource
    private BizAccountInfoRepository bizAccountInfoRepository;

    @Resource
    private BizAccountUserRepository bizAccountUserRepository;

    @Resource
    private BizAccountUserLogRepository bizAccountUserLogRepository;

    @Resource
    private SequenceService sequenceService;

    @Resource
    private AuthPlatformService authPlatformService;

    @Resource
    private ToolRemoveMsgPub toolRemoveMsgPub;

    @Resource
    private AdAccountInfoGrpcService adAccountInfoGrpcService;

    @Resource
    private AccountMemberGrpcService accountMemberGrpcService;

    /**
     * 企业号角色ID
     */
    @DynamicValue
    @Value("${masterRoleId}")
    private Long masterRoleId;

    /**
     * 员工号角色ID
     */
    @DynamicValue
    @Value("${staffRoleId}")
    private Long staffRoleId;

    @DynamicValue
    @Value("${onlineConsultUserIdWhiteList}")
    private String onlineConsultUserIdWhiteList;

    @DynamicValue
    @Value("${privilegeIndustryList}")
    private String privilegeIndustryList;

    @Resource
    private StaffConsumer staffConsumer;
    @Autowired
    private BizAccountPrivilegeInfoRepository bizAccountPrivilegeInfoRepository;

    @DynamicValue
    @Value("${mgk.privilege.maxPrivilegeCount:200}")
    private Integer MAX_PRIVILEGE_COUNT;

    /**
     * 查询用户经营号信息，如果没有绑定经营号返回null
     *
     * @param uid
     * @return
     */
    public BizAccountUserInfo queryAccountUserInfo(Long uid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        if (Objects.isNull(bizAccountUser)) {
            return null;
        }

        BizAccountInfo accountInfo = bizAccountInfoRepository.selectByAccountId(bizAccountUser.getAccountId());
        if (Objects.isNull(accountInfo)) {
            log.warn("未查询到经营号主体信息 uid={}", uid);
            return null;
        }

        List<String> authList = FunctionUtil.splitByComma(bizAccountUser.getAuth(), String::valueOf);
        //企业号有所有权限
        if (Objects.equals(RoleTypeEnum.MASTER.getType(), bizAccountUser.getRoleType())) {
            authList = AuthTypeEnum.allCodes();
        }

        //检查在线咨询白名单
        boolean hasNoOnlineConsult = StringUtils.isNotBlank(onlineConsultUserIdWhiteList) && !onlineConsultUserIdWhiteList.contains(uid.toString());
        if (hasNoOnlineConsult && authList.contains(AuthTypeEnum.ONLINE_CONSULT.getCode())) {
            log.info("queryAccountUserInfo hit no hasOnlineConsult, uid:{}", uid);
            authList.remove(AuthTypeEnum.ONLINE_CONSULT.getCode());
        }
        //
        List<Integer> awakenAppList = adAccountInfoGrpcService.queryAdAccountAwakenApp(bizAccountUser.getAdId());
        if (CollectionUtils.isEmpty(awakenAppList)) {
            authList.remove(AuthTypeEnum.APP_PACKAGE.getCode());
        }

        if (!authList.contains(AuthTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode())) {
            authList.add(AuthTypeEnum.THIRD_PARTY_LANDING_PAGE_URL.getCode());
        }

        Integer hasAssistant;

        if (Objects.equals(RoleTypeEnum.STAFF.getType(), bizAccountUser.getRoleType())) {
            hasAssistant = BooleanUtils.toInteger(Boolean.FALSE);
        } else {
            hasAssistant = accountInfo.getHasAssistant();
        }

        return BizAccountUserInfo.builder()
                .accountId(bizAccountUser.getAccountId())
                .adId(accountInfo.getAdId())
                .masterUid(accountInfo.getUid())
                .uid(bizAccountUser.getUid())
                .authList(authList)
                .remarkName(bizAccountUser.getRemarkName())
                .roleType(bizAccountUser.getRoleType())
                .bindTime(bizAccountUser.getBindTime())
                .hasAssistant(hasAssistant)
                .build();
    }

    /**
     * 查询用户经营号信息，如果没有绑定广告账户抛出异常提醒
     *
     * @param uid 非空
     * @return BizAccountUserInfo 非空
     */
    public BizAccountUserInfo checkUserAdId(@NonNull Long uid) {
        BizAccountUserInfo accountUserInfo = queryAccountUserInfo(uid);
        if (Objects.isNull(accountUserInfo)) {
            throw new ClientViewException(BizErrorCode.UN_LINK_ACCOUNT.getCode(), BizErrorCode.UN_LINK_ACCOUNT.getMsg());
        }
        if (Objects.isNull(accountUserInfo.getAdId()) || accountUserInfo.getAdId().equals(0L)) {
            throw new ClientViewException(BizErrorCode.UN_LINK_AD_ACCOUNT.getCode(), BizErrorCode.UN_LINK_AD_ACCOUNT.getMsg());
        }
        return accountUserInfo;
    }

    /**
     * 新增一个经营号
     */
    @Transactional(rollbackFor = Exception.class, value = "metaShardingDataSourceTransactionManager")
    public void doAddAccount(Long uid, Long adId) {
        doAddAccount(uid, adId, null, null);
    }

    /**
     * 新增一个经营号
     */
    @Transactional(rollbackFor = Exception.class, value = "metaShardingDataSourceTransactionManager")
    public void doAddAccount(Long uid, Long adId, Integer accountType, Integer hasAssistant) {
        BizAccountInfo accountInfo = bizAccountInfoRepository.selectByAdId(adId);
        if (Objects.nonNull(accountInfo)) {
            throw new ClientViewException("该广告账号已经绑定过经营号");
        }
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        if (Objects.nonNull(bizAccountUser)) {
            throw new ClientViewException("该登录账号已经绑定过经营号");
        }

        //经营号唯一ID
        Long accountId = sequenceService.nextId(SequenceNames.BIZ_ACCOUNT_ID);

        Map<Integer, AccountIndustryItem> adAccountIndustryMap = adAccountInfoGrpcService.queryAdAccountIndustryInfo(Collections.singletonList(adId.intValue()));
        AccountIndustryItem accountIndustryItem = adAccountIndustryMap.get(adId.intValue());
        if (Objects.isNull(accountIndustryItem)) {
            throw new ClientViewException("广告账号行业信息不存在");
        }

        Map<Integer, String> adAccountCustomerNameMap = adAccountInfoGrpcService.queryAdAccountCustomerName(Collections.singletonList(adId.intValue()));
        String adAccountCustomerName = adAccountCustomerNameMap.get(adId.intValue());
        if (StringUtils.isBlank(adAccountCustomerName)) {
            throw new ClientViewException("广告客户信息不存在");
        }

        Map<Long, String> uidNameMap = accountMemberGrpcService.queryMidNameMap(Collections.singletonList(uid));
        String uidName = uidNameMap.getOrDefault(uid, "");

        // 新增经营号
        BizAccountInfo.BizAccountInfoBuilder builder = BizAccountInfo.builder();
        builder.accountId(accountId)
                .adId(adId)
                .uid(uid)
                .isDelete(0);
        if (Objects.nonNull(accountType)) {
            builder.type(accountType);
        }
        if (Objects.nonNull(hasAssistant)) {
            builder.hasAssistant(hasAssistant);
        }

        BizAccountInfo info = builder.build();
        bizAccountInfoRepository.save(info);

        // 新增经营号主账号
        BizAccountUser accountUser =
                BizAccountUser.builder()
                        .accountId(accountId)
                        .uid(uid)
                        .roleType(RoleTypeEnum.MASTER.getType())
                        .auth("")
                        .bindTime(new Date())
                        .isDelete(0)
                        .firstIndustryId(accountIndustryItem.getUnitedFirstIndustryId())
                        .secondIndustryId(accountIndustryItem.getUnitedSecondIndustryId())
                        .adCustomerName(adAccountCustomerName)
                        .uidName(uidName)
                        .adId(adId.intValue())
                        .build();
        bizAccountUserRepository.save(accountUser);

        //注册企业角色权限
        authPlatformService.modifyGroupsForUser(uid, Collections.singletonList(masterRoleId));


        if (StringUtils.isBlank(privilegeIndustryList)) {
            return;
        }

        Set<Integer> privilegeIndustrySet = JSON.parseObject(privilegeIndustryList, Set.class);
        if (!privilegeIndustrySet.contains(accountIndustryItem.getUnitedFirstIndustryId())) {
            log.info("doAddAccount: Industry not eligible for privilege. uid={}, industryId={}", uid, accountIndustryItem.getUnitedFirstIndustryId());
            return;
        }
        Integer privilegeCount = bizAccountPrivilegeInfoRepository.queryAllValidPrivilegeCount();
        if (privilegeCount >= MAX_PRIVILEGE_COUNT) {
            log.info("doAddAccount: Privilege count exceeds maximum limit. uid={}, currentCount={}, maxCount={}", uid, privilegeCount, MAX_PRIVILEGE_COUNT);
            return;
        }
        List<BizAccountPrivilegeInfoPo> bizAccountPrivilegeInfoPos = bizAccountPrivilegeInfoRepository.selectBizAccountPrivilege(Collections.singletonList(uid), null, null);
        if (CollectionUtils.isNotEmpty(bizAccountPrivilegeInfoPos)) {
            log.info("doAddAccount: User already in privilege whitelist. uid={}", uid);
            return;
        }
        bizAccountPrivilegeInfoRepository.addBizAccountPrivilege(Collections.singletonList(uid), "system");

    }


    /**
     * 绑定员工号
     *
     * @param tokenInfo
     */
    @Transactional(rollbackFor = Exception.class, value = "metaShardingDataSourceTransactionManager")
    public BizAccountUser doBindStaff(UserTokenInfo tokenInfo) {

        BizAccountUser masterBizAccountUser = bizAccountUserRepository.selectByUid(tokenInfo.getInviterUid());
        Integer masterAdId = masterBizAccountUser.getAdId();

        Map<Integer, AccountIndustryItem> adAccountIndustryMap = adAccountInfoGrpcService.queryAdAccountIndustryInfo(Collections.singletonList(masterAdId));
        AccountIndustryItem accountIndustryItem = adAccountIndustryMap.get(masterAdId);
        if (Objects.isNull(accountIndustryItem)) {
            throw new ClientViewException("广告账号行业信息不存在");
        }

        Map<Integer, String> adAccountCustomerNameMap = adAccountInfoGrpcService.queryAdAccountCustomerName(Collections.singletonList(masterAdId));
        String adAccountCustomerName = adAccountCustomerNameMap.get(masterAdId);
        if (StringUtils.isBlank(adAccountCustomerName)) {
            throw new ClientViewException("广告客户信息不存在");
        }

        Map<Long, String> uidNameMap = accountMemberGrpcService.queryMidNameMap(Collections.singletonList(tokenInfo.getUid()));
        String uidName = uidNameMap.getOrDefault(tokenInfo.getUid(), "");

        // 员工绑定
        BizAccountUser staff = BizAccountUser.builder()
                .accountId(tokenInfo.getAccountId())
                .uid(tokenInfo.getUid())
                .remarkName(tokenInfo.getRemarkName())
                .roleType(RoleTypeEnum.STAFF.getType())
                .auth(FunctionUtil.joinByByComma(tokenInfo.getAuthList(), String::valueOf))
                .bindTime(new Date())
                .firstIndustryId(accountIndustryItem.getUnitedFirstIndustryId())
                .secondIndustryId(accountIndustryItem.getUnitedSecondIndustryId())
                .adCustomerName(adAccountCustomerName)
                .uidName(uidName)
                .adId(masterAdId)
                .build();
        bizAccountUserRepository.save(staff);

        //开通员工权限
        authPlatformService.modifyGroupsForUser(tokenInfo.getUid(), Collections.singletonList(staffRoleId));

        // log
        BizAccountUserOpLog userOpLog =
                BizAccountUserOpLog.builder()
                        .uid(tokenInfo.getInviterUid())
                        .opUid(tokenInfo.getUid())
                        .opType(UserOpTypeEnum.BIND.getType())
                        .detail(JSON.toJSONString(staff))
                        .build();
        bizAccountUserLogRepository.saveAsync(userOpLog);
        return staff;
    }

    /**
     * 解绑员工
     *
     * @param request
     * @param loginUid 操作者
     */
    @Transactional(rollbackFor = Exception.class, value = "metaShardingDataSourceTransactionManager")
    public void doUnBindStaff(StaffUnbindRequest request, Long loginUid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(request.getStaffUid());
        if (Objects.isNull(bizAccountUser)) {
            throw new ClientViewException("未找到绑定的账号信息");
        }

        BizAccountUser updateUser = new BizAccountUser();
        updateUser.setId(bizAccountUser.getId());
        updateUser.setIsDelete(1);
        bizAccountUserRepository.updateById(updateUser);

        //取消角色权限
        authPlatformService.modifyGroupsForUser(request.getStaffUid(), Collections.emptyList());

        //减少权限需要通知直播
        ToolRemoveMsg removeMsg =
                ToolRemoveMsg.builder()
                        .uid(request.getStaffUid())
                        .unBindStaff(true)
                        .build();
        Message message =
                Message.Builder.of(String.valueOf(request.getStaffUid()), removeMsg).build();
        toolRemoveMsgPub.pubWithRetry(message);

        //log
        BizAccountUserOpLog userOpLog =
                BizAccountUserOpLog.builder()
                        .uid(loginUid)
                        .opUid(request.getStaffUid())
                        .opType(UserOpTypeEnum.UNBIND.getType())
                        .detail(JSON.toJSONString(updateUser))
                        .build();
        bizAccountUserLogRepository.saveAsync(userOpLog);
    }

    public PageInfo<BizAccountUserInfo> queryMasterAccountInfo(Integer pageNum, Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<BizAccountUser> list = bizAccountUserRepository.queryMasterAccount();
        PageInfo<BizAccountUser> bizAccountUserPageInfo = new PageInfo<>(list);
        if (CollectionUtils.isEmpty(bizAccountUserPageInfo.getList())) {
            return new PageInfo<>();
        }
        List<BizAccountUserInfo> transform = bizAccountUserPageInfo.getList().stream().map(item ->
                BizAccountUserInfo.builder()
                        .accountId(item.getAccountId())
                        .masterUid(item.getUid())
                        .uid(item.getUid())
                        .remarkName(item.getRemarkName())
                        .roleType(item.getRoleType())
                        .build()
        ).collect(Collectors.toList());
        PageInfo<BizAccountUserInfo> result = new PageInfo<>();
        BeanUtils.copyProperties(bizAccountUserPageInfo, result);
        result.setList(transform);
        return result;
    }

    public Integer queryAccountShopIdDefaultZero(Long uid) {
        try {
            return queryAccountShopInfo(uid).getShopId();
        } catch (Exception e) {
            log.error("queryAccountShopIdDefaultZero error", e);
            return 0;
        }
    }

    public BizAccountUserShopInfo queryAccountShopInfo(Long uid) {
        BizAccountUser bizAccountUser = bizAccountUserRepository.selectByUid(uid);
        if (Objects.isNull(bizAccountUser)) {
            throw new ClientViewException("未查询到经营号主体信息");
        }

        Long accountId = bizAccountUser.getAccountId();
        StaffMainQuery staffMainQuery = new StaffMainQuery();
        staffMainQuery.setBizId(CustomerBizIdEnum.BUSINESS_ACCOUNT.getType());
        staffMainQuery.setMainMid(uid);
        staffMainQuery.setParentShopId(accountId);
        BaseResponse<StaffMainDTO> response = staffConsumer.getStaffMain(staffMainQuery);

        if (Objects.isNull(response) || Objects.isNull(response.data) || Objects.isNull(response.data.getShopId())) {
            throw new com.bilibili.mall.common.exception.ClientViewException("获取客服主体信息失败");
        }
        Integer shopId = response.data.getShopId();
        BizAccountUserShopInfo bizAccountUserShopInfo = new BizAccountUserShopInfo();
        bizAccountUserShopInfo.setAccountId(accountId);
        bizAccountUserShopInfo.setShopId(shopId);
        bizAccountUserShopInfo.setUid(bizAccountUser.getUid());
        return bizAccountUserShopInfo;


    }
}
