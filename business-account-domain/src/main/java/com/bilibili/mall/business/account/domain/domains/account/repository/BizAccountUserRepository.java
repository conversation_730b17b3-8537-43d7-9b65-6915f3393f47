package com.bilibili.mall.business.account.domain.domains.account.repository;

import com.bilibili.mall.business.account.infrastructure.entity.BizAccountUser;
import com.bilibili.mall.business.account.infrastructure.mapper.BizAccountUserMapper;
import com.bilibili.mall.common.exception.ClientViewException;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/1/31 7:32 下午
 */
@Component
public class BizAccountUserRepository {
    @Resource
    private BizAccountUserMapper bizAccountUserMapper;

    private static final Integer IS_DELETE = 1;
    private static final Integer NOT_DELETE = 0;

    public BizAccountUser selectByUid(Long uid) {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.UID, uid)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        List<BizAccountUser> accountInfos = bizAccountUserMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(accountInfos)) {
            return null;
        }
        return accountInfos.get(0);
    }

    public List<BizAccountUser> selectByUidList(List<Long> uids) {
        if (CollectionUtils.isEmpty(uids)) {
            return Collections.emptyList();
        }
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andIn(BizAccountUser.UID, uids)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        return bizAccountUserMapper.selectByExample(example);
    }

    public List<BizAccountUser> selectByUserIds(Long accountId, List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.ACCOUNT_ID, accountId)
                .andIn(BizAccountUser.UID, userIds)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        return bizAccountUserMapper.selectByExample(example);
    }


    public List<BizAccountUser> selectByAccountIdAndRole(Long accountId, Integer roleType) {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.ACCOUNT_ID, accountId)
                .andEqualTo(BizAccountUser.ROLE_TYPE, roleType)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        example.orderBy("id").desc();
        return bizAccountUserMapper.selectByExample(example);
    }

    public Integer countByAccountIdAndRole(Long accountId, Integer roleType) {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.ACCOUNT_ID, accountId)
                .andEqualTo(BizAccountUser.ROLE_TYPE, roleType)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        return bizAccountUserMapper.selectCountByExample(example);
    }


    public void save(BizAccountUser bizAccountInfo) {
        int i = bizAccountUserMapper.insertSelective(bizAccountInfo);
        if (i <= 0) {
            throw new ClientViewException("保存账户信息失败");
        }
    }

    public void updateById(BizAccountUser bizAccountInfo) {
        if (Objects.isNull(bizAccountInfo) || Objects.isNull(bizAccountInfo.getId())) {
            throw new ClientViewException("更新账户信息失败");
        }
        int i = bizAccountUserMapper.updateByPrimaryKeySelective(bizAccountInfo);
        if (i <= 0) {
            throw new ClientViewException("更新账户信息失败");
        }
    }

    public void updateByUid(BizAccountUser bizAccountInfo) {
        if (Objects.isNull(bizAccountInfo) || Objects.isNull(bizAccountInfo.getUid())) {
            throw new ClientViewException("更新账户信息失败");
        }
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.UID, bizAccountInfo.getUid())
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        int i = bizAccountUserMapper.updateByExampleSelective(bizAccountInfo, example);
        if (i <= 0) {
            throw new ClientViewException("更新账户信息失败");
        }
    }

    /**
     * 分页查询, 不能直接调, 必须有PageMethod.startPage(pageNum, pageSize);
     *
     * @return
     */
    public List<BizAccountUser> queryMasterAccount() {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.ROLE_TYPE, 1)
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        example.setOrderByClause(" id desc ");
        return bizAccountUserMapper.selectByExample(example);
    }

    public List<BizAccountUser> selectAll() {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria()
                .andEqualTo(BizAccountUser.IS_DELETE, 0);
        example.selectProperties(BizAccountUser.C_TIME, BizAccountUser.ACCOUNT_ID, BizAccountUser.UID, BizAccountUser.AUTH, BizAccountUser.AD_ID, BizAccountUser.ROLE_TYPE);
        return bizAccountUserMapper.selectByExample(example);
    }

    public List<BizAccountUser> queryBusinessAccount(Long uid, String uidName, List<Integer> firstIndustryId, List<Integer> secondIndustryId,
                                                     Long beginCtime, Long endCtime, Integer roleType, Integer adId,
                                                     String adCustomerName, Integer isDelete, Integer page, Integer pageSize) {
        Example example = new Example(BizAccountUser.class);
        Example.Criteria criteria = example.createCriteria();
        if (uid > 0) {
            criteria.andEqualTo(BizAccountUser.UID, uid);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstIndustryId)) {
            criteria.andIn(BizAccountUser.FIRST_INDUSTRY_ID, firstIndustryId);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(secondIndustryId)) {
            criteria.andIn(BizAccountUser.SECOND_INDUSTRY_ID, secondIndustryId);
        }
        if (beginCtime > 0) {
            criteria.andGreaterThanOrEqualTo(BizAccountUser.C_TIME, new Date(beginCtime));
        }
        if (endCtime > 0) {
            criteria.andLessThanOrEqualTo(BizAccountUser.C_TIME, new Date(endCtime));
        }
        if (roleType > 0) {
            criteria.andEqualTo(BizAccountUser.ROLE_TYPE, roleType);
        }
        if (adId > 0) {
            criteria.andEqualTo(BizAccountUser.AD_ID, adId);
        }
        if (Objects.equals(isDelete, IS_DELETE)) {
            criteria.andEqualTo(BizAccountUser.IS_DELETE, IS_DELETE);
        } else if (Objects.equals(isDelete, NOT_DELETE)) {
            criteria.andEqualTo(BizAccountUser.IS_DELETE, NOT_DELETE);
        }

        if (StringUtils.isNotBlank(adCustomerName)) {
            criteria.andEqualTo(BizAccountUser.AD_CUSTOMER_NAME, adCustomerName);
        }
        if (StringUtils.isNotBlank(uidName)) {
            criteria.andEqualTo(BizAccountUser.UID_NAME, uidName);
        }
        example.setOrderByClause(" ctime desc ");

        if (page > 0 && pageSize > 0) {
            RowBounds rowBound = new RowBounds((page - 1) * pageSize, pageSize);
            return bizAccountUserMapper.selectByExampleAndRowBounds(example, rowBound);
        } else {
            return bizAccountUserMapper.selectByExample(example);
        }
    }

    public Integer queryBusinessAccountCount(Long uid, String uidName, List<Integer> firstIndustryId, List<Integer> secondIndustryId,
                                             Long beginCtime, Long endCtime, Integer roleType, Integer adId,
                                             String adCustomerName, Integer isDelete) {
        Example example = new Example(BizAccountUser.class);
        Example.Criteria criteria = example.createCriteria();
        if (uid > 0) {
            criteria.andEqualTo(BizAccountUser.UID, uid);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(firstIndustryId)) {
            criteria.andIn(BizAccountUser.FIRST_INDUSTRY_ID, firstIndustryId);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(secondIndustryId)) {
            criteria.andIn(BizAccountUser.SECOND_INDUSTRY_ID, secondIndustryId);
        }
        if (beginCtime > 0) {
            criteria.andGreaterThanOrEqualTo(BizAccountUser.C_TIME, new Date(beginCtime));
        }
        if (endCtime > 0) {
            criteria.andLessThanOrEqualTo(BizAccountUser.C_TIME, new Date(endCtime));
        }
        if (roleType > 0) {
            criteria.andEqualTo(BizAccountUser.ROLE_TYPE, roleType);
        }
        if (adId > 0) {
            criteria.andEqualTo(BizAccountUser.AD_ID, adId);
        }
        if (Objects.equals(isDelete, IS_DELETE)) {
            criteria.andEqualTo(BizAccountUser.IS_DELETE, IS_DELETE);
        } else if (Objects.equals(isDelete, NOT_DELETE)) {
            criteria.andEqualTo(BizAccountUser.IS_DELETE, NOT_DELETE);
        }

        if (StringUtils.isNotBlank(adCustomerName)) {
            criteria.andEqualTo(BizAccountUser.AD_CUSTOMER_NAME, adCustomerName);
        }
        if (StringUtils.isNotBlank(uidName)) {
            criteria.andEqualTo(BizAccountUser.UID_NAME, uidName);
        }


        return bizAccountUserMapper.selectCountByExample(example);
    }


    public Long selectBizAccountUserMaxId() {
        Example example = new Example(BizAccountUser.class);
        example.orderBy("id").desc();
        RowBounds rowBounds = new RowBounds(0, 1);
        List<BizAccountUser> bizAccountUsers = bizAccountUserMapper.selectByExampleAndRowBounds(example, rowBounds);
        if (CollectionUtils.isEmpty(bizAccountUsers)) {
            return 0L;
        }
        return bizAccountUsers.get(0).getId();
    }

    public List<BizAccountUser> selectBizAccountUserByIdRange(Long startId, Long endId) {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria().andBetween("id", startId, endId);
        return bizAccountUserMapper.selectByExample(example);
    }

    public List<BizAccountUser> queryByAdId(Long adId) {
        Example example = new Example(BizAccountUser.class);
        example.createCriteria().andEqualTo(BizAccountUser.AD_ID, adId)
                .andEqualTo(BizAccountUser.IS_DELETE, NOT_DELETE);
        return bizAccountUserMapper.selectByExample(example);
    }
}
