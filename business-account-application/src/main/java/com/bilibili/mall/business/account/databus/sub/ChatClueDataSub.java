package com.bilibili.mall.business.account.databus.sub;

import com.alibaba.fastjson.JSONObject;
import com.bapis.videoup.open.service.GetArchiveReply;
import com.bapis.videoup.open.service.GetArchiveReq;
import com.bilibili.mall.business.account.api.vo.account.UserLoginAccountVO;
import com.bilibili.mall.business.account.api.vo.clue.ChatClueMsgBo;
import com.bilibili.mall.business.account.databus.dto.LauArchiveCommentConversionComponentBo;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AccountMemberGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.VideoUpGrpcService;
import com.bilibili.mall.business.account.domain.databus.sub.BaseDatabusListener;
import com.bilibili.mall.business.account.domain.domains.account.entity.BizAccountUserInfo;
import com.bilibili.mall.business.account.domain.domains.account.service.BizAccountDomainService;
import com.bilibili.mall.business.account.infrastructure.ad_mapper.ManagerChatClueMapper;
import com.bilibili.mall.business.account.infrastructure.entity.ManagerChatCluePo;
import com.bilibili.mall.business.account.infrastructure.utils.BVIDUtils;
import com.bilibili.mall.business.account.service.BizAccountService;
import com.bilibili.mall.business.account.service.impl.BizAccountServiceImpl;
import com.bilibili.warp.databus.AckableMessage;
import com.bilibili.warp.spring.boot.autoconfigure.databus.DatabusProperties;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
public class ChatClueDataSub extends BaseDatabusListener {

    // 定义正则表达式
    String regex = "\\d{11}";

    @Resource
    private ManagerChatClueMapper managerChatClueMapper;

    @Resource
    private AccountMemberGrpcService accountMemberGrpcService;

    @Resource
    private VideoUpGrpcService videoUpGrpcService;

    @Resource
    private BizAccountDomainService bizAccountDomainService;

    @Resource
    private BizAccountService bizAccountService;

    protected ChatClueDataSub(DatabusProperties properties) {
        super(properties);
    }

    @Override
    public void onMessage(AckableMessage message) {

        try {
            String msg = new String(message.payload(), "UTF-8");
            log.info("ChatClueDataSub message:{}", msg);
            String value = new String(message.payload());
            JSONObject msgJsonObj = JSONObject.parseObject(value);
            ChatClueMsgBo chatClueMsgBo = deserializeBinlogDto(msgJsonObj);
            if (Objects.isNull(chatClueMsgBo)) {
                log.info("ChatClueDataSub deserializeBinlogDto fail {}", msgJsonObj);
                message.ack();
                return;
            }

            Example example = new Example(ManagerChatCluePo.class);
            example.createCriteria().andEqualTo("msgKey", chatClueMsgBo.getMsg_key());
            List<ManagerChatCluePo> managerChatCluePos = managerChatClueMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(managerChatCluePos)) {
                log.info("ChatClueDataSub msgKey exist {}", chatClueMsgBo);
                message.ack();
                return;
            }

            String phoneNo = matchPhoneNo(chatClueMsgBo);
            long avid = 0;
            try {
                avid = BVIDUtils.bvToAv(chatClueMsgBo.getBvid());
            } catch (Exception e) {
                log.info("ChatClueDataSub bvToAv fail", e);
//                message.ack();
//                return;
            }

            ManagerChatCluePo managerChatCluePo = buildPo(avid, chatClueMsgBo, phoneNo);
            if (managerChatCluePo == null) {
                message.ack();
                return;
            }

            managerChatClueMapper.insertSelective(managerChatCluePo);
            message.ack();
        } catch (Exception e) {
            log.error("ChatClueDataSub fail", e);
        }
    }

    private ManagerChatCluePo buildPo(long avid, ChatClueMsgBo chatClueMsgBo, String phoneNo) {
        String title = "";
        if (avid > 0) {
            GetArchiveReply reply =
                    videoUpGrpcService.getArchive(GetArchiveReq.newBuilder().setAid(avid).build());
            title = reply.getTitle();
        }

        ManagerChatCluePo managerChatCluePo = new ManagerChatCluePo();
        managerChatCluePo.setMid(Long.valueOf(chatClueMsgBo.getMid()));

        Map<Long, String> midNameMap = accountMemberGrpcService.queryMidNameMap(Lists.newArrayList(Long.parseLong(chatClueMsgBo.getMid())));
        managerChatCluePo.setMidName(midNameMap.get(Long.parseLong(chatClueMsgBo.getMid())));
        managerChatCluePo.setAvid(avid);
        managerChatCluePo.setTitle(title);
        managerChatCluePo.setMsgKey(chatClueMsgBo.getMsg_key());
        managerChatCluePo.setPhoneNo(phoneNo);
        managerChatCluePo.setIp(chatClueMsgBo.getIp());
        managerChatCluePo.setClueCtime(chatClueMsgBo.getClue_ctime());

        BizAccountUserInfo bizAccountUserInfo = bizAccountDomainService.queryAccountUserInfo(chatClueMsgBo.getUid());
        if (null == bizAccountUserInfo) {
            log.error("非经营号账号ChatClueDataSub queryAccountUserInfo fail {}", chatClueMsgBo.getUid());
            return null;
        }
        managerChatCluePo.setUid(chatClueMsgBo.getUid());
        managerChatCluePo.setMasterUid(bizAccountUserInfo.getMasterUid());
        if (chatClueMsgBo.getUid().equals(bizAccountUserInfo.getMasterUid())) {
            //主账号
            managerChatCluePo.setSourceType((byte) 1);
        } else {
            //子账号
            managerChatCluePo.setSourceType((byte) 2);
        }
        return managerChatCluePo;
    }

    private String matchPhoneNo(ChatClueMsgBo chatClueMsgBo) {
        // 编译正则表达式
        Pattern pattern = Pattern.compile(regex);
        // 创建匹配器
        Matcher matcher = pattern.matcher(chatClueMsgBo.getMsg_content());
        // 查找并打印所有匹配的结果
        String phoneNo = "";
        if (matcher.find()) {
            phoneNo = matcher.group();
        } else {
            log.info("ChatClueDataSub phoneNo not found {}", chatClueMsgBo.getMsg_content());
        }
        return phoneNo;
    }

    @Override
    protected String alias() {
        return "business-account-chat-clue";
    }

    private ChatClueMsgBo deserializeBinlogDto(JSONObject jsonObject) {
        if (Objects.isNull(jsonObject)) {
            return null;
        }
        return jsonObject.toJavaObject(ChatClueMsgBo.class);
    }
}
