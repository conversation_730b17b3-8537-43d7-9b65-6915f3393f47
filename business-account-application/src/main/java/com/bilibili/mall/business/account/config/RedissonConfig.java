package com.bilibili.mall.business.account.config;

import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.config.Config;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

/**
 * Created by fan<PERSON><PERSON> on 2017/6/22.
 */
@Configuration
@ComponentScan
public class RedissonConfig {
    @Value("${redisson.cluster.nodes}")
    private String redisNodes;

    @Value("${redisson.timeout}")
    private Integer timeout;
    @Value("${redisson.maxIdle}")
    private Integer redisMaxIdle;

    @Value("${redisson.minIdle}")
    private Integer redisMinIdle;

    @Bean(destroyMethod = "shutdown")
    RedissonClient redissonClient() throws IOException {
        Config config = new Config();
        config.useClusterServers()
                .setMasterConnectionPoolSize(redisMaxIdle)
                .setMasterConnectionMinimumIdleSize(redisMinIdle)
                .setConnectTimeout(timeout)
                .addNodeAddress(redisNodes.split(","));
        return Redisson.create(config);
    }
}
