package com.bilibili.mall.business.account.controller;

import com.bapis.ad.crm.customer.PromotionType;
import com.bilibili.mall.business.account.api.request.account.*;
import com.bilibili.mall.business.account.api.service.BizAccountRemoteService;
import com.bilibili.mall.business.account.api.vo.account.*;
import com.bilibili.mall.business.account.converter.CrmConverter;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.dto.*;
import com.bilibili.mall.business.account.domain.constants.OauthConstants;
import com.bilibili.mall.business.account.dto.*;
import com.bilibili.mall.business.account.infrastructure.utils.AppRequestUtils;
import com.bilibili.mall.business.account.service.BizAccountService;
import com.bilibili.mall.common.response.BaseResponse;
import com.github.pagehelper.PageInfo;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/31 2:27 下午
 */
@Slf4j
@RestController
public class BizAccountController implements BizAccountRemoteService {

    @Autowired
    private BizAccountService bizAccountService;

    @Resource
    private HttpServletRequest servletRequest;

    @Override
    public BaseResponse<UserLoginAccountVO> queryLoginUserInfo() {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        UserLoginAccountVO loginAccountVO = bizAccountService.queryLoginUserInfo(loginUid);
        fakeUname(loginAccountVO, loginUid);
        return new BaseResponse<>(loginAccountVO);
    }

    @Override
    public BaseResponse<String> checkWhiteList() {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        bizAccountService.checkFakeLoginUid(loginUid);
        return new BaseResponse<>("success");
    }

    @ApiOperation("退出登录")
    @GetMapping("/logout")
    public BaseResponse<String> logout(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            cookie.setMaxAge(0);
            cookie.setDomain(".bilibili.com");
            cookie.setPath("/");
            response.addCookie(cookie);
        }
        return new BaseResponse<>("success");
    }

    @ApiOperation("经营号退出伪登录")
    @GetMapping("/fake/logout")
    public BaseResponse<String> fakeLogout(HttpServletRequest request, HttpServletResponse response) {
        Cookie[] cookies = request.getCookies();
        for (Cookie cookie : cookies) {
            if (OauthConstants.MANAGER_ID.equalsIgnoreCase(cookie.getName())) {
                cookie.setMaxAge(0);
                cookie.setDomain(".bilibili.com");
                cookie.setPath("/");
            }
            if (OauthConstants.MANAGER_NAME.equalsIgnoreCase(cookie.getName())) {
                cookie.setMaxAge(0);
                cookie.setDomain(".bilibili.com");
                cookie.setPath("/");
            }
            response.addCookie(cookie);
        }
        return new BaseResponse<>("success");
    }

    @ApiOperation("经营号伪登录")
    @GetMapping("/fake/login")
    public BaseResponse<String> fakeLogin(Long managerUid, HttpServletResponse response) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        UserLoginAccountVO accountVO = bizAccountService.fakeLogin(loginUid, managerUid);
        setCookie(accountVO, response);
        return new BaseResponse<>("success");
    }

    @Override
    public BaseResponse<PageInfo<StaffInfoVO>> queryStaffList(@Valid StaffQueryRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.queryStaffList(request, loginUid));
    }

    /**
     * 员工账号绑定
     *
     * @param request
     */
    @Override
    public BaseResponse<BindUserInfoVO> queryToBindInfo(@Valid StaffInfoRequest request) {

        return new BaseResponse<>(bizAccountService.queryToBindInfo(request));
    }

    @Override
    public BaseResponse<StaffInviteInfoVO> inviteUser(@Valid StaffInviteRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.inviteUser(request, loginUid));
    }

    @Override
    public BaseResponse<StaffInviteStatusInfoVO> queryInviteStatus(@Valid StaffInviteQueryRequest request) {

        return new BaseResponse<>(bizAccountService.queryInviteStatus(request));
    }

    @Override
    public BaseResponse<TokenInfoVO> queryTokenInfo(@Valid TokenInfoQueryRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.queryTokenInfo(request, loginUid));
    }

    @Override
    public BaseResponse<Void> acceptTokenInfo(@Valid TokenInfoAcceptRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.acceptTokenInfo(request, loginUid));
    }

    /**
     * 员工账号授权修改
     *
     * @param request
     */
    @Override
    public BaseResponse<Void> updateStaffAuth(@Valid StaffAuthRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.updateStaffAuth(request, loginUid));
    }

    /**
     * 员工备注修改
     *
     * @param request
     */
    @Override
    public BaseResponse<Void> updateStaffRemark(@Valid StaffRemarkRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.updateStaffRemark(request, loginUid));
    }

    /**
     * 员工账号解绑
     *
     * @param request
     */
    @Override
    public BaseResponse<Void> unBindStaff(@Valid StaffUnbindRequest request) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        return new BaseResponse<>(bizAccountService.unBindStaff(request, loginUid));
    }

    private void fakeUname(UserLoginAccountVO loginAccountVO, Long loginUid) {
        String loginUname = AppRequestUtils.getUnameByRequest(servletRequest);
        if (StringUtils.isNotBlank(loginUname)) {
            try {
                loginAccountVO.setUserNickName(URLDecoder.decode(loginUname, "UTF-8"));
            } catch (Throwable throwable) {
                log.error("loginUname 解码失败. loginUid = {}, loginUname:{}", loginUid, loginUname, throwable);
            }
        }
    }

    private void setCookie(UserLoginAccountVO accountVO, HttpServletResponse response) {
        try {
            String managerName = accountVO.getUserNickName() + "(伪登录中)";
            Cookie cookieId = new Cookie("manager_id", accountVO.getUid().toString());
            cookieId.setDomain(".bilibili.com");
            cookieId.setPath("/");

            Cookie cookieName = new Cookie("manager_name", URLEncoder.encode(managerName, StandardCharsets.UTF_8.toString()));
            cookieName.setDomain(".bilibili.com");
            cookieName.setPath("/");
            response.addCookie(cookieId);
            response.addCookie(cookieName);
        } catch (Throwable throwable) {
            log.error("encode managerName fail. managerId:{}, managerName:{}", accountVO.getUid(), accountVO.getUserNickName(), throwable);
        }
    }

    @Override
    public BaseResponse<Boolean> accountOpenAuth(AccountOpenAuthRequest accountOpenAuthRequest) {

        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        bizAccountService.accountOpenAuth(loginUid, accountOpenAuthRequest.getAuthType());

        return new BaseResponse<>(Boolean.TRUE);
    }

    @Override
    public BaseResponse<AccountStatusVO> queryAccountStatus(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        AccountStatusDto accountStatusDto = bizAccountService.queryAccountStatus(loginUid);
        AccountStatusVO accountStatusVO = new AccountStatusVO();
        BeanUtils.copyProperties(accountStatusDto, accountStatusVO);
        return new BaseResponse<>(accountStatusVO);
    }


    @Override
    public BaseResponse<Integer> registerEnterpriseAccount(RegisterEnterpriseAccountReqVO reqVo) {
        RegisterEnterpriseAccountDto registerEnterpriseAccountDto = CrmConverter.convertor.convertToRegisterEnterpriseAccountDto(reqVo);
        if (Objects.equals(reqVo.getPromotionType(), PromotionType.ONLINE_STORE_VALUE)) {
            registerEnterpriseAccountDto.setIcpRecordPics(reqVo.getStoreQualificationPics());
            registerEnterpriseAccountDto.setDomain(reqVo.getStoreUrl());
        }
        if (StringUtils.isBlank(reqVo.getIcpRecordNumber())) {
            registerEnterpriseAccountDto.setIcpRecordNumber(registerEnterpriseAccountDto.getBusinessLicenseCode());
        }
        if (StringUtils.isBlank(reqVo.getDomain())) {
            registerEnterpriseAccountDto.setDomain("https://www.bilibili.com");
        }
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        registerEnterpriseAccountDto.setUid(loginUid);
        Integer result = bizAccountService.registerEnterpriseAccount(registerEnterpriseAccountDto);
        return new BaseResponse<>(result);
    }

    @Override
    public BaseResponse<Integer> registerPersonalAccount(RegisterPersonalAccountReqVO reqVo) {
        RegisterPersonalAccountDto registerPersonalAccountDto = CrmConverter.convertor.convertToRegisterPersonalAccountDto(reqVo);
        if (StringUtils.isBlank(reqVo.getDomain())) {
            registerPersonalAccountDto.setDomain("https://www.bilibili.com");
        }
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        registerPersonalAccountDto.setUid(loginUid);
        Integer result = bizAccountService.registerPersonalAccount(registerPersonalAccountDto);
        return new BaseResponse<>(result);
    }

    @ApiOperation("获取手机验证码")
    @GetMapping("/getPhoneVerificationCode")
    public BaseResponse<Integer> getPhoneVerificationCode(String phoneNumber) {
        String ipAddress = getIPAddress(servletRequest);
        log.info("phoneNumber: [{}], ip {}", phoneNumber, ipAddress);
        Integer phoneVerificationCode = bizAccountService.getPhoneVerificationCode(ipAddress, phoneNumber);
        return new BaseResponse<>(phoneVerificationCode);
    }

    @Override
    public BaseResponse<CheckRegisterAccountVO> checkRegisterEnterpriseAccount(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);

        CheckRegisterAccountDto checkRegisterAccountDto = bizAccountService.checkRegisterEnterpriseAccount(loginUid);
        CheckRegisterAccountVO checkRegisterAccountVO = CheckRegisterAccountVO.builder()
                .code(checkRegisterAccountDto.getCode())
                .msg(checkRegisterAccountDto.getMsg())
                .build();
        return new BaseResponse<>(checkRegisterAccountVO);
    }

    @Override
    public BaseResponse<CheckRegisterAccountVO> checkRegisterPersonalAccount(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);

        CheckRegisterAccountDto checkRegisterAccountDto = bizAccountService.checkRegisterPersonalAccount(loginUid);
        CheckRegisterAccountVO checkRegisterAccountVO = CheckRegisterAccountVO.builder()
                .code(checkRegisterAccountDto.getCode())
                .msg(checkRegisterAccountDto.getMsg())
                .build();
        return new BaseResponse<>(checkRegisterAccountVO);
    }

    @Override
    public BaseResponse<BlueVInfoVO> queryBlueVInfo(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);

        UserOfficialInfoDto userOfficialInfoDto = bizAccountService.queryUserOfficialInfo(loginUid);
        BlueVInfoVO blueVInfoVO = CrmConverter.convertor.convertToBlueVInfoVOFromUserOfficialInfoDto(userOfficialInfoDto);
        return new BaseResponse<>(blueVInfoVO);
    }

    @Override
    public BaseResponse<BihuoEnterpriseInfoVO> queryBihuoEnterpriseInfo(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);

        CrmBihuoQualificationDto crmBihuoQualificationDto = bizAccountService.queryBihuoEnterpriseInfo(loginUid);
        BihuoEnterpriseInfoVO bihuoEnterpriseInfoVO = CrmConverter.convertor.convertToBihuoEnterpriseInfoVO(crmBihuoQualificationDto);
        return new BaseResponse<>(bihuoEnterpriseInfoVO);
    }

    @Override
    public BaseResponse<Integer> updatePersonalAccountQualification(RegisterPersonalAccountReqVO reqVo) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        RegisterPersonalAccountDto registerPersonalAccountDto = CrmConverter.convertor.convertToRegisterPersonalAccountDto(reqVo);
        if (StringUtils.isBlank(reqVo.getDomain())) {
            registerPersonalAccountDto.setDomain("https://www.bilibili.com");
        }
        registerPersonalAccountDto.setUid(loginUid);
        Integer result = bizAccountService.updatePersonalAccount(registerPersonalAccountDto);

        return new BaseResponse<>(result);
    }

    @Override
    public BaseResponse<Integer> updateEnterpriseAccountQualification(RegisterEnterpriseAccountReqVO reqVo) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        RegisterEnterpriseAccountDto registerEnterpriseAccountDto = CrmConverter.convertor.convertToRegisterEnterpriseAccountDto(reqVo);
        if (Objects.equals(reqVo.getPromotionType(), PromotionType.ONLINE_STORE_VALUE)) {
            registerEnterpriseAccountDto.setIcpRecordPics(reqVo.getStoreQualificationPics());
            registerEnterpriseAccountDto.setDomain(reqVo.getStoreUrl());
        }
        if (StringUtils.isBlank(reqVo.getIcpRecordNumber())) {
            registerEnterpriseAccountDto.setIcpRecordNumber(reqVo.getBusinessLicenseCode());
        }
        registerEnterpriseAccountDto.setUid(loginUid);
        Integer result = bizAccountService.updateEnterpriseAccount(registerEnterpriseAccountDto);

        return new BaseResponse<>(result);
    }

    @Override
    public BaseResponse<AccountQualificationVO> queryAccountQualification(Long uid) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);

        AccountQualificationDto accountQualificationDto = bizAccountService.queryCustomerInfoDetail(loginUid);
        AccountQualificationVO accountQualificationVO = CrmConverter.convertor.convertToAccountQualificationVO(accountQualificationDto);

        return new BaseResponse<>(accountQualificationVO);
    }

    @Override
    public BaseResponse<List<IndustryVo>> queryFirstIndustry() {
        List<IndustryInfoDto> industryInfoDtoList = bizAccountService.queryFirstIndustry();
        List<IndustryVo> industryVoList = industryInfoDtoList.stream()
                .map(dto -> IndustryVo.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .build())
                .collect(Collectors.toList());
        return new BaseResponse<>(industryVoList);
    }

    @Override
    public BaseResponse<List<IndustryVo>> queryIndustryByPid(Integer pid) {
        List<IndustryInfoDto> industryInfoDtoList = bizAccountService.queryIndustryByPid(pid);
        List<IndustryVo> industryVoList = industryInfoDtoList.stream()
                .map(dto -> IndustryVo.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .build())
                .collect(Collectors.toList());
        return new BaseResponse<>(industryVoList);
    }

    @Override
    public BaseResponse<List<CrmQualificationTypeVO>> getQualificationList() {
        List<CrmQualificationTypeDto> qualificationList = bizAccountService.getQualificationList();
        List<CrmQualificationTypeVO> voList = qualificationList.stream()
                .map(dto -> CrmQualificationTypeVO.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .build())
                .collect(Collectors.toList());
        return new BaseResponse<>(voList);
    }

    @Override
    public BaseResponse<List<CrmQualificationTypeVO>> getSpecialInfoTypeList() {
        List<CrmQualificationTypeDto> specialInfoTypeList = bizAccountService.getSpecialInfoTypeList();
        List<CrmQualificationTypeVO> voList = specialInfoTypeList.stream()
                .map(dto -> CrmQualificationTypeVO.builder()
                        .id(dto.getId())
                        .name(dto.getName())
                        .build())
                .collect(Collectors.toList());
        return new BaseResponse<>(voList);
    }

    public String getIPAddress(HttpServletRequest request) {
        String ip = null;
        //X-Forwarded-For：Squid 服务代理
        String ipAddresses = request.getHeader("X-Forwarded-For");
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //Proxy-Client-IP：apache 服务代理
            ipAddresses = request.getHeader("Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //WL-Proxy-Client-IP：weblogic 服务代理
            ipAddresses = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //HTTP_CLIENT_IP：有些代理服务器
            ipAddresses = request.getHeader("HTTP_CLIENT_IP");
        }
        if (ipAddresses == null || ipAddresses.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            //X-Real-IP：nginx服务代理
            ipAddresses = request.getHeader("X-Real-IP");
        }
        //有些网络通过多层代理，那么获取到的ip就会有多个，一般都是通过逗号（,）分割开来，并且第一个ip为客户端的真实IP
        if (ipAddresses != null && ipAddresses.length() != 0) {
            ip = ipAddresses.split(",")[0];
        }
        //还是不能获取到，最后再通过request.getRemoteAddr();获取
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ipAddresses)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    @Override
    public BaseResponse<BiliUserInfoVo> queryBiliInfo() {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        BiliUserInfoDto biliUserInfoDto = bizAccountService.queryBiliUserInfo(loginUid);

        return new BaseResponse<>(BiliUserInfoVo.builder().userName(biliUserInfoDto.getUserName()).face(biliUserInfoDto.getFace()).uid(String.valueOf(loginUid)).build());
    }

    @Override
    public BaseResponse<AccountQualificationVO> queryAccountQualificationVO(String businessLicenceCode, String personalIdCardNumber) {
        AccountQualificationDto accountQualificationDto = bizAccountService.queryCustomerInfoDetail(businessLicenceCode, personalIdCardNumber);
        AccountQualificationVO accountQualificationVO = CrmConverter.convertor.convertToAccountQualificationVO(accountQualificationDto);

        return new BaseResponse<>(accountQualificationVO);
    }

    @Override
    public BaseResponse<Boolean> authBlueVInfo() {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        Boolean result = bizAccountService.authBlueVInfo(loginUid);
        return new BaseResponse<>(result);
    }

    @Override
    public BaseResponse<Boolean> authBihuoInfo() {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        Boolean result = bizAccountService.authBihuoInfo(loginUid);
        return new BaseResponse<>(result);
    }

    @Override
    public BaseResponse<RealNameInfoCheckResponseVO> realNameInfoCheck(String idCardNumber, String realName) {
        Long loginUid = AppRequestUtils.getMidAndValidLogin(servletRequest);
        RealNameInfoCheckDto realNameInfoCheckDto = bizAccountService.realNameInfoCheck(idCardNumber, realName, loginUid);
        RealNameInfoCheckResponseVO realNameInfoCheckResponseVO = new RealNameInfoCheckResponseVO();
        BeanUtils.copyProperties(realNameInfoCheckDto, realNameInfoCheckResponseVO);
        return new BaseResponse<>(realNameInfoCheckResponseVO);
    }

    @Override
    public BaseResponse<PersonalAccountCrmInfoVO> queryCrmPersonalExistAccount(String idCardNumber) {
        PersonalAccountCrmInfoDto personalAccountCrmInfoDto = bizAccountService.queryCrmPersonalExistAccount(idCardNumber);
        if (Objects.isNull(personalAccountCrmInfoDto)) {
            return new BaseResponse<>(null);
        }
        PersonalAccountCrmInfoVO personalAccountCrmInfoVO = new PersonalAccountCrmInfoVO();
        BeanUtils.copyProperties(personalAccountCrmInfoDto, personalAccountCrmInfoVO);
        return new BaseResponse<>(personalAccountCrmInfoVO);
    }
}
