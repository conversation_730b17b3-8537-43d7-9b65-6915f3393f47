package com.bilibili.mall.business.account.grpc.service;

import com.bapis.ad.business.acc.privilege.*;
import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bilibili.mall.business.account.api.enums.RoleTypeEnum;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AccountMemberGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdAccountInfoGrpcService;
import com.bilibili.mall.business.account.domain.anticorruption.grpc.AdIndustryGrpcService;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountPrivilegeInfoRepository;
import com.bilibili.mall.business.account.domain.domains.account.repository.BizAccountUserRepository;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountPrivilegeInfoPo;
import com.bilibili.mall.business.account.infrastructure.entity.BizAccountUser;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@RPCService
@Slf4j
public class PrivilegeGrpcService extends PrivilegeServiceGrpc.PrivilegeServiceImplBase {

    @Resource
    private BizAccountPrivilegeInfoRepository bizAccountPrivilegeInfoRepository;

    @Resource
    private BizAccountUserRepository bizAccountUserRepository;
    @Autowired
    private AdAccountInfoGrpcService adAccountInfoGrpcService;
    @Autowired
    private AdIndustryGrpcService adIndustryGrpcService;
    @Autowired
    private AccountMemberGrpcService accountMemberGrpcService;

    @DynamicValue
    @Value("${mgk.privilege.maxPrivilegeCount:200}")
    private Integer MAX_PRIVILEGE_COUNT;

    public static final String MAX_PRIVILEGE_MSG = "数量已达上限";

    @Override
    public void queryPrivilegeList(QueryPrivilegeListReq request, StreamObserver<QueryPrivilegeListReply> responseObserver) {
        List<Long> queryMidList = request.getMidListList();
        long startTime = request.getStartTime();
        long endTime = request.getEndTime();
        Page<Object> page = PageHelper.startPage(request.getPageNum(), request.getPageSize());
        List<BizAccountPrivilegeInfoPo> bizAccountPrivilegeInfoPos = bizAccountPrivilegeInfoRepository.selectBizAccountPrivilege(queryMidList, startTime, endTime);
        if (CollectionUtils.isEmpty(bizAccountPrivilegeInfoPos)) {
            QueryPrivilegeListReply queryPrivilegeListReply = QueryPrivilegeListReply.newBuilder()
                    .addAllList(new ArrayList<>())
                    .setTotal(0)
                    .build();
            responseObserver.onNext(queryPrivilegeListReply);
            responseObserver.onCompleted();
            return;
        }

        List<Long> midList = bizAccountPrivilegeInfoPos.stream().map(BizAccountPrivilegeInfoPo::getUid).collect(Collectors.toList());

        List<BizAccountUser> bizAccountUsers = bizAccountUserRepository.selectByUidList(midList);
        Map<Long, BizAccountUser> uidUserMap = bizAccountUsers.stream().collect(Collectors.toMap(BizAccountUser::getUid, Function.identity()));
        List<Integer> adIdList = bizAccountUsers.stream().map(BizAccountUser::getAdId).distinct().collect(Collectors.toList());
        Map<Integer, AccountIndustryItem> adIdIndustryMap = adAccountInfoGrpcService.queryAdAccountIndustryInfo(adIdList);
        HashSet<Integer> industryIdSet = new HashSet<>();
        for (AccountIndustryItem accountIndustryItem : adIdIndustryMap.values()) {
            industryIdSet.add(accountIndustryItem.getUnitedFirstIndustryId());
            industryIdSet.add(accountIndustryItem.getUnitedSecondIndustryId());
            industryIdSet.add(accountIndustryItem.getUnitedThirdIndustryId());
        }
        Map<Integer, String> industryIdNameMap = adIndustryGrpcService.queryIndustryMap(new ArrayList<>(industryIdSet));
        Map<Long, String> midNameMap = accountMemberGrpcService.queryMidNameMap(midList);

        Map<Integer, String> adAccountCustomerNameMap = adAccountInfoGrpcService.queryAdAccountCustomerName(adIdList);

        List<PrivilegeInfo> privilegeInfoList = new ArrayList<>();

        for (BizAccountPrivilegeInfoPo bizAccountPrivilegeInfoPo : bizAccountPrivilegeInfoPos) {
            Long mid = bizAccountPrivilegeInfoPo.getUid();

            BizAccountUser bizAccountUser = uidUserMap.get(mid);
            if (Objects.isNull(bizAccountUser)) {
                continue;
            }
            AccountIndustryItem accountIndustryItem = adIdIndustryMap.get(bizAccountUser.getAdId());
            PrivilegeInfo privilegeInfo = PrivilegeInfo.newBuilder()
                    .setMid(mid)
                    .setMidName(midNameMap.getOrDefault(mid, ""))
                    .setRoleType(bizAccountUser.getRoleType())
                    .setRoleTypeName(RoleTypeEnum.getByType(bizAccountUser.getRoleType()).getMngDesc())
                    .setAdId(bizAccountUser.getAdId())
                    .setCustomerName(adAccountCustomerNameMap.getOrDefault(bizAccountUser.getAdId(), ""))
                    .setFirstIndustryName(industryIdNameMap.getOrDefault(accountIndustryItem.getUnitedFirstIndustryId(), ""))
                    .setSecondIndustryName(industryIdNameMap.getOrDefault(accountIndustryItem.getUnitedSecondIndustryId(), ""))
                    .setThirdIndustryName(industryIdNameMap.getOrDefault(accountIndustryItem.getUnitedThirdIndustryId(), ""))
                    .setOperator(bizAccountPrivilegeInfoPo.getOperator())
                    .setCtime(bizAccountPrivilegeInfoPo.getCtime().getTime())
                    .build();
            privilegeInfoList.add(privilegeInfo);
        }


        Long total = page.getTotal();
        QueryPrivilegeListReply queryPrivilegeListReply = QueryPrivilegeListReply.newBuilder()
                .addAllList(privilegeInfoList)
                .setTotal(total.intValue())
                .build();
        responseObserver.onNext(queryPrivilegeListReply);
        responseObserver.onCompleted();
    }

    @Override
    public void addPrivilege(AddPrivilegeReq request, StreamObserver<AddPrivilegeReply> responseObserver) {
        try {
            log.info("PrivilegeGrpcService addPrivilege request={}", request);
            List<Long> midList = request.getMidList();
            String operator = request.getOperator();

            List<AddPrivilegeSingleReply> addPrivilegeSingleReplyList = new ArrayList<>();

            List<BizAccountPrivilegeInfoPo> existPrivilegeList = bizAccountPrivilegeInfoRepository.queryAllValidPrivilegeList();
            Set<Long> existPrivilegeUidSet = existPrivilegeList.stream().map(BizAccountPrivilegeInfoPo::getUid).distinct().collect(Collectors.toSet());
            existPrivilegeUidSet.addAll(midList);

            if (existPrivilegeUidSet.size() > MAX_PRIVILEGE_COUNT) {
                AddPrivilegeSingleReply addPrivilegeSingleReply = AddPrivilegeSingleReply.newBuilder()
                        .addAllMid(midList)
                        .setMsg(MAX_PRIVILEGE_MSG)
                        .build();
                addPrivilegeSingleReplyList.add(addPrivilegeSingleReply);
            }
            List<BizAccountUser> bizAccountUsers = bizAccountUserRepository.selectByUidList(midList);
            Map<Long, BizAccountUser> uidUserMap = bizAccountUsers.stream().collect(Collectors.toMap(BizAccountUser::getUid, Function.identity()));

            List<Long> notExistMidList = midList.stream().filter(mid -> !uidUserMap.containsKey(mid)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(notExistMidList)) {
                AddPrivilegeSingleReply addPrivilegeSingleReply = AddPrivilegeSingleReply.newBuilder()
                        .addAllMid(notExistMidList)
                        .setMsg("mid未注册经营号")
                        .build();
                addPrivilegeSingleReplyList.add(addPrivilegeSingleReply);
            }

            if (CollectionUtils.isEmpty(addPrivilegeSingleReplyList)) {
                List<BizAccountPrivilegeInfoPo> bizAccountPrivilegeInfoPos = bizAccountPrivilegeInfoRepository.selectBizAccountPrivilege(midList, null, null);
                Set<Long> existUid = bizAccountPrivilegeInfoPos.stream().map(BizAccountPrivilegeInfoPo::getUid).collect(Collectors.toSet());
                List<Long> notExistMid = midList.stream().filter(mid -> !existUid.contains(mid)).collect(Collectors.toList());
                bizAccountPrivilegeInfoRepository.updateBizAccountPrivilege(new ArrayList<>(existUid), operator);
                bizAccountPrivilegeInfoRepository.addBizAccountPrivilege(notExistMid, operator);


            }

            AddPrivilegeReply addPrivilegeReply = AddPrivilegeReply.newBuilder()
                    .addAllReplies(addPrivilegeSingleReplyList)
                    .build();
            responseObserver.onNext(addPrivilegeReply);
            responseObserver.onCompleted();
        } catch (Exception e) {
            responseObserver.onError(new StatusRuntimeException(Status.fromCode(Status.Code.INTERNAL)
                    .withDescription(e.getMessage())
                    .withCause(e)));
            log.info("PrivilegeGrpcService addPrivilege error request={}", request, e);

        }
    }

    @Override
    public void deletePrivilege(DeletePrivilegeReq request, StreamObserver<DeletePrivilegeReply> responseObserver) {
        List<Long> deleteMidList = request.getMidListList();
        bizAccountPrivilegeInfoRepository.deleteBizAccountPrivilege(deleteMidList);
        DeletePrivilegeReply deletePrivilegeReply = DeletePrivilegeReply.newBuilder()
                .setCode(0)
                .build();
        responseObserver.onNext(deletePrivilegeReply);
        responseObserver.onCompleted();
    }
}
