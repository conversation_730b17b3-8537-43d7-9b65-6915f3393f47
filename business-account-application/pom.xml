<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.bilibili.mall</groupId>
        <artifactId>business-account-parent</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>business-account-application</artifactId>
    <packaging>jar</packaging>
    <name>business-account-application</name>

    <dependencies>
        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>business-account-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>business-account-domain</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>business-account-infrastructure</artifactId>
        </dependency>

        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>kraken-web-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>retrofit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-scalars</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-gson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>logging-interceptor</artifactId>
        </dependency>
        <!-- GRPC组件 -->
        <dependency>
            <groupId>com.bilibili.mall</groupId>
            <artifactId>kraken-grpc-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>passport_service.identify_grpc_java</artifactId>
            <version>${bapi.version}</version>
        </dependency>

        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_business.acc_grpc_java</artifactId>
            <version>1.29.0.1.mr22167.17465331700000.ecd1abac0da9</version>
        </dependency>
        <dependency>
            <groupId>co.bilibili.buf</groupId>
            <artifactId>ad_business.acc.privilege_grpc_java</artifactId>
            <version>1.29.0.1.mr22243.17452082470000.377aa03e6068</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- MapStruct dependencies -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>business-account</finalName>
        <extensions>
            <extension>
                <groupId>kr.motd.maven</groupId>
                <artifactId>os-maven-plugin</artifactId>
                <version>1.7.0</version>
            </extension>
        </extensions>
        <plugins>
            <!-- 添加 spring boot 打包插件 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.3.RELEASE</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Configure the Maven Compiler Plugin for MapStruct -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.0</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.20</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.5.5.Final</version>
                        </path>

                        <!-- Additional annotation processors if needed -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>com.bilibili</groupId>-->
            <!--                <artifactId>buf-maven-plugin</artifactId>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <goals>-->
            <!--                            <goal>generate</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash; 此处modules仅为示例！！！根据项目实际依赖的proto文件的module名来填写 &ndash;&gt;-->
            <!--                    <moduleArray>-->
            <!--                        &lt;!&ndash; 用户鉴权 &ndash;&gt;-->
            <!--                        <array>buf.bilibili.co/passport/service.identify</array>-->
            <!--                        <array>buf.bilibili.co/ad/business.acc</array>-->
            <!--                    </moduleArray>-->
            <!--&lt;!&ndash;                    <osClassifier>osx-x86_64</osClassifier>&ndash;&gt;-->
            <!--                </configuration>-->
            <!--            </plugin>-->
        </plugins>
    </build>

</project>