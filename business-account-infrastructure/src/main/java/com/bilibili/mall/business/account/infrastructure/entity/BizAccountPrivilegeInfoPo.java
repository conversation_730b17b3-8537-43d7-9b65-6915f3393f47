package com.bilibili.mall.business.account.infrastructure.entity;


import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.Table;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "business_account_privilege_info")
public class BizAccountPrivilegeInfoPo extends BaseModel{

    public static final String UID = "uid";

    public static final String OPERATOR = "operator";

    public static final String IS_DELETED = "isDeleted";

    private Long uid;

    private String operator;

    private Integer isDeleted;
}
